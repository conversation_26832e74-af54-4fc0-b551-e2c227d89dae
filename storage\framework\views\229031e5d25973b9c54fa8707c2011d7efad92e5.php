<?php $__env->startPush('plugins-css'); ?>
	<style type="text/css">
		.salary {
			text-align: right;
		}

		.table-header {
			/* vertical-align: middle; */
			text-align: center;
		}
		.th2 {
			background-color: #becdf2;
		}
		.table-header2{
			background-color: #becdf2;
			color: black;		
		}
		@media  screen and (max-width: 767px) {
			.salary-mb {
				padding: 0;
			}
			.salary-mb .table tr th, .salary-mb .table tr td {
				padding: 5px 2px;
				font-size: 8px;
			}
		}

		#payslip-format-create-modal .title-setting{
			height: 30px !important;
		}

		#payslip-format-create-modal .section{
			margin-top: 10px;
			margin-bottom: 15px;
			display: flex;
			width: 100%;
			flex-direction: column;
		}
		#payslip-format-create-modal .section-wrapper{
			width: 100%;
			display: flex;
			flex-wrap: wrap;
		}
		#payslip-format-create-modal .section .section-wrapper .item{
			width: calc(100% / 7 + 1px);
			display:flex;
			flex-direction: column;
			border: 1px solid black;
			margin-top: -1px;
			margin-left: -1px;
		}    

		
		#payslip-format-create-modal .section .section-wrapper .item .title{
			width: 100%;
			height: 45px;
			display: flex;
			background-color: #becdf2;
			justify-content: center;
			align-items: center;
			border-bottom: 1px solid black;
		}
		#payslip-format-create-modal .section .section-wrapper .item .title .title-setting{
			width: 85%;
		}
		#payslip-format-create-modal .section .section-wrapper .item .value{
			width: 100%;
			height: 35px;
			justify-content: center;
			align-items: center;
		}
		#payslip-format-create-modal .addrow-wrapper{
			margin-top: 5px;
			width: 100%;
			display: flex;
			justify-content: flex-end;
		}
		#payslip-format-create-modal .section-4{
			display: flex;
			widows: 100%;
			justify-content: space-between;
		}
		#payslip-format-create-modal .section-4 .left{
			width: 13%;
			display: flex;
			flex-direction: column;
		}
		#payslip-format-create-modal .section-4 .right{
			width: 85%;
		}
		#payslip-format-create-modal .section-4 .item{
			display: flex;
			width: 100%;
			border: 1px solid black;
			margin-top: -1px;
			margin-left: -1px;

		}
		#payslip-format-create-modal .section-4 .item .title{
			width: 75%;
			height: 45px;
			display: flex;
			padding-left: 10px;
			padding-right: 10px;
			border-right: 1px solid black;
			justify-content: center;
			align-items: center;
			background-color: #becdf2;
		}
		#payslip-format-create-modal .section-4 .right .table{
			margin: 0px;
		}
		#payslip-format-create-modal .section-4 .right .table td{
			height: 35px;
			padding: 8px;
		}
		
	</style>
<?php $__env->stopPush(); ?>
<div class="tg-modal tg-modal__large content-center box--shadow" id="create-csv-guidline-modal">
	<div class="container tg-modal__container">
        <table class="table table-bordered">
            <thead class="thead-cyan">
                <tr>
                    <th scope="col" class="text-center" width="12%">社員コード</th>
                    <th scope="col" class="text-center" width="12%">アカウント番号</th>
                    <th scope="col" class="text-center" width="12%">氏名</th>
                    <th scope="col" class="text-center" width="10%">フォーマットID</th>
                    <th scope="col" class="text-center" width="12%">所属</th>
                    <th scope="col" class="text-center" width="10%">役職</th>
                    <th scope="col" class="text-center" width="12%">出入対象給料</th>
                    <th scope="col" class="text-center" width="10%">支払日</th>
                    <th scope="col" class="text-center" width="10%">基準給区部</th>
                </tr>
            </thead>
            <tbody id="tbody">
              <tr>
                <td>
                    従業員・スタッフの社員コードを 
                    入力してください。 <br> <br>

                    例：123456
                </td>
                <td>
                    従業員かスタッフをシステム内で
                    区別するための項目です。<br><br>

                    従業員の場合：1 <br>
                    スタッフの場合：100 
                </td>
                <td>
                    従業員・スタッフの氏名を
                    入力してください。<br><br>

                    例：山田太郎
                </td>
                <td>
                    フォーマット登録時のID 
                    を入力してください。<br><br>

                    例：1
                </td>
                <td>
                    従業員・スタッフの所属部署を 
                    入力してください。<br><br>

                    例：株式会社○○/開発部
                </td>
                <td>
                    従業員・スタッフの所属部署を 
                    入力してください。<br><br>

                    例：一般 <br>
                    例：課長 <br>
                    例：部長
                </td>
                <td>
                    読み込みデータの対象年月を入力してください。 <br><br>

                    例：202201
                </td>
                <td>
                    給与日を入力してください。 <br><br>

                    例：25
                </td>
                <td>
                    給与形態を入力してください。 <br> <br>

                    例：月例 <br>
                    例：賞与
                </td>
              </tr>
            </tbody>
        </table>
	</div>
</div><?php /**PATH E:\TOOLS\laragon\www\control_management\resources\views/Staff/Paylist/Includes/create-csv-guidline-modal.blade.php ENDPATH**/ ?>