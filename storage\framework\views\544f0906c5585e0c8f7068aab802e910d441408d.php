<?php $__env->startSection('page-title'); ?>
給与明細
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb-menu'); ?>
給与明細管理
<?php $__env->stopSection(); ?>

<?php $__env->startPush('plugins-css'); ?>

<?php $__env->stopPush(); ?>

<?php $__env->startPush('custom-css'); ?>
    <style>
        table th {
            text-transform: uppercase;
            background: none;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('page-content'); ?>

<section class="tg-section tg-payList__section">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="box__container box--shadow">
                    <div class="box__body">

                        <ul class="tg-nav-tab nav nav-tabs justify-content-lg-end mb-4" id="nav-tab" role="tablist">
						
                            <li class="nav-item" role="presentation">
                                <a class="nav-link active" id="employer-code-tab" data-toggle="tab" href="#employer-code" role="tab" aria-controls="employer-code" aria-selected="true">社員コード更新（スタッフアカウント）</a>
                            </li>

                        	<?php if(checkRoleUser(config("constants.ROLE.csv-import-edit.alias"))): ?>
								<li class="nav-item" role="presentation">
									<a class="nav-link" id="csv-import-tab" data-toggle="tab" href="#csv-import" role="tab" aria-controls="csv-import" >CSV取込</a>
								</li>
							<?php endif; ?>
							
                        	<li class="nav-item" role="presentation">
                        		<a class="nav-link" id="payslip-tab" data-toggle="tab" href="#payslip" role="tab" aria-controls="payslip" aria-selected="false">給与明細</a>
                        	</li>
                        	
                        </ul>

                        <div class="tab-content" id="nav-tabContent">
						
                            <div class="tab-pane fade show active" id="employer-code" role="tabpanel" aria-labelledby="employer-code-tab">
                                <?php echo $__env->make('Staff.Paylist.Includes.tab-employer-code', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
						

                        	<?php if(checkRoleUser(config("constants.ROLE.csv-import-edit.alias"))): ?>
								<div class="tab-pane fade" id="csv-import" role="tabpanel" aria-labelledby="csv-import-tab">
									<?php echo $__env->make('Staff.Paylist.Includes.tab-csv-import', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
								</div>
							<?php endif; ?>

                        	<div class="tab-pane fade" id="payslip" role="tabpanel" aria-labelledby="payslip-tab">
								<div id="payslip-content">
								</div>
                        	</div>
                        	
                        </div>
                    </div>
                </div>
            </div>
        </div>
	</div>
	<div class="tg-modal content-center box--shadow" id="payslip_modal">
		<div class="container tg-modal__container" id="payslip_result"></div>
	</div>

	<div class="tg-modal content-center box--shadow" id="employee_master_modal">
		<div class="container tg-modal__container" id="employee_master_result"></div>
	</div>

	<div class="tg-modal content-center box--shadow" id="employee_master_confirmation_modal">
		<div class="container tg-modal__container" id="employee_master_confirmation_result"></div>
	</div>

	<div class="tg-modal content-center box--shadow" id="register_email_modal">
		<div class="container tg-modal__container" id="register_email_result"></div>
	</div>

	
	<div class="tg-modal box--shadow" id="emp_import_error">
		<div class="container tg-modal__container" id="modal-body">
			<div class="icon-error col-md-3">
				<span class="line line-left"></span>
				<span class="line line-right"></span>
			</div>
			<fieldset class="fieldset__group emp-import container tg-modal__container pt-0 mt-3" style="border: 1px solid #d04a2f;">
				<legend class="m-auto"><h2 style="color: #d04a2f;">エラー</h2></legend>
				<div class="m-0" style="min-height: 50px; max-height: 250px; color: #d04a2f;" id="import_error">
					
				</div>
			</fieldset> 
			<div class="col-md-2 ml-auto p-2">
				<button class="btn btn-primary md-2" data-izimodal-close="" style=" background-color: #78cbf2;">OK</button>
			</div>
		</div>
	</div>
</section>

<style>   
    .wap_staff_not_exist{
        max-height: 300px;
        overflow-y: scroll;
    }

	#admin_import_error .icon-error{
		position: relative;
		margin: auto;
		width: 50px;
		height: 50px;
		border-radius: 50%;
		border: 4px solid #f27474;
	}

	#admin_import_error .icon-error .line{
		position: absolute;
		left: 10%;
		top: 47%;
		height: 4px;
		width: 35px;
		background-color: #f27474;
	}

	#admin_import_error .icon-error .line-right{
		-webkit-transform: rotate(-45deg);
    	transform: rotate(-45deg);
	}

	#admin_import_error .icon-error .line-left{
		-webkit-transform: rotate(45deg);
    	transform: rotate(45deg);
	}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-modal'); ?>
    <?php echo $__env->make('Includes.department-by-group', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('plugins-js'); ?>

<?php $__env->stopPush(); ?>

<?php $__env->startPush('custom-js'); ?>
<script src="<?php echo e(asset('js/PayList/paylist.js')); ?>"></script>
<script>
	const URL_IMPORT = "<?php echo e(route('upload-employer-code')); ?>"
	const URL_EXPORT = "<?php echo e(route('dowload-csv-employer')); ?>"
    const URL_EXPORT_2 = "<?php echo e(route('dowload-csv-employer-2')); ?>"
	$(document).ready(function () {
		if($('#csv-import .alert').length > 0){
            $('#csv-import-tab').click();
        }

		var salary_type = '<?php echo e(session('salary_type')); ?>';
		var s_month = '<?php echo e(session('month')); ?>';
		if(salary_type === '1'){
			$('.label_month').hide();
			for (let month = 1; month < 13 ; month++) {
				$("#month_dropdown option[value='"+month+"']").remove();
			}
			var select1 = parseInt(s_month) == 1 ? "selected" : "";
			var select2 =  parseInt(s_month) == 2 ? "selected" : "";
			$("#month_dropdown").append('<option '+select1+' value="1">①</option>');
			$("#month_dropdown").append('<option '+select2+' value="2">➁</option>');
		}else{
			$('.label_month').show();
			for (let month = 1; month < 13 ; month++) {
				$("#month_dropdown option[value='"+month+"']").remove();
			}
			for (let month = 1; month < 13 ; month++) {
				var selected  = (parseInt(s_month) == month) ? 'selected':"";
				$("#month_dropdown").append('<option '+selected+' value="'+month+'">'+month+'</option>');
			}
		}

		$('#salary_type').change(function (e) { 
			e.preventDefault();
			var type = $(this).val();
			if(type === '1'){
				$('.label_month').hide();
				for (let month = 1; month < 13 ; month++) {
					$("#month_dropdown option[value='"+month+"']").remove();
				}
				$("#month_dropdown").append('<option value="1">①</option>');
				$("#month_dropdown").append('<option value="2">➁</option>');
			}else{
				$('.label_month').show();
				for (let month = 1; month < 13 ; month++) {
					$("#month_dropdown option[value='"+month+"']").remove();
				}
				for (let month = 1; month < 13 ; month++) {
					$("#month_dropdown").append('<option value="'+month+'">'+month+'</option>');
				}
			}
		});

		var is_accountant = 'true';
		var tab_opened = 'payslip-tab';
		if(is_accountant==='false'){
			$.ajax({
				method: "GET",
				url : 'open-payslip-tab',
				beforeSend : function(){

				},
				data:{
					tab_opened : tab_opened
				}
			}).done(function(response){
				if(tab_opened=='payslip-tab')
				{
					$("body").find("div#nav-tabContent > div#payslip > div#payslip-content").html(response);
					PayList.paylist_datatable();
				}
			}).fail(function(){

			});
		}
	});

    <?php if(session('staff_not_exist')): ?>
        const wrapper = document.createElement('div');
        wrapper.classList.add('wap_staff_not_exist');
        wrapper.innerHTML = "<?php echo session('staff_not_exist'); ?>";
        swal({
            title : 'ポータル上にユーザーがいないため、取り込めませんでした',     
            content: wrapper,
            icon: "warning",
            buttons: {
                confirm: {
                    text: '戻る'
                },
            },
            dangerMode: true,
        });
    <?php endif; ?>
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('Layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\TOOLS\laragon\www\control_management\resources\views/Staff/Paylist/paylist.blade.php ENDPATH**/ ?>