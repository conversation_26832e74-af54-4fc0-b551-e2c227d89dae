<style type="text/css">
	.salary {
		text-align: right;
	}

	.table-header {
		/* vertical-align: middle; */
		text-align: center;
	}
	.th2 {
		background-color: #becdf2;
	}
	.table-header2{
		background-color: #becdf2;
		color: black;		
	}
	@media  screen and (max-width: 767px) {
		.salary-mb {
			padding: 0;
		}
		.salary-mb .table tr th, .salary-mb .table tr td {
			padding: 5px 2px;
			font-size: 8px;
		}
	}
</style>
<?php if(!empty($p_data)): ?>
<?php
    $year = $p_data->cd_salary_year ?? 0;
    $month = $p_data->cd_salary_month ?? 0;
    $s_user_type = session()->get('s_user_type'); 
?>

<div class="col-md-12 salary-mb">
	<div class="row">
		<div style="margin:0px;" class="col-md-6">
			<h3> <?php echo e($year); ?>年<?php echo e($month); ?>月分 <?php echo e($t_data->work_title); ?>明細書</h3>
		</div>
		<div class="col-md-6" style="text-align:right;">
			<?php if($s_user_type == 1 || $s_user_type == 2 ): ?>
			<form action="download-payslip-pdf" method="post" target="_blank">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="cd_salary_id" value="<?php echo e($p_data->cd_salary_id); ?>"/>
				<input type="submit" class="btn btn-primary" value="PDF出力"/>
			</form>
			<?php endif; ?>
		</div>
	</div>
	<div class="" style="margin-top:10px;">
		<table style="border:black solid 1px;" class="table">
			<thead>
				<tr style="background-color:#17a2b8;border:black solid 1px;">
					<th style="color:black;border:black solid 1px; text-align:center;" class="th2 width150">部門 ‐ 所属</th>
					<th style="color:black;border:black solid 1px; text-align:center;" class="th2 width150">社員コード</th>
					<th style="color:black;border:black solid 1px;" class="th2" align="left">氏名</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td style="border:black solid 1px;"><?php echo e($p_data->cd_salary_e); ?></td>
					<td style="border:black solid 1px;"><?php echo e($p_data->cd_salary_a); ?></td>
					<td style="border:black solid 1px;"><?php echo e($user_info->user_name ?? ''); ?>　様</td>
				</tr>
			</tbody>
		</table>

		支給
		<table style="border:black solid 1px;  margin-top:10px;" id="provide_table" class="table">
			<thead>
				<tr style="background-color:#17a2b8;border:black solid 1px;">
					<td style="color:black;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						基本給
					</td>
					<td style="color:black;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						役員報酬<br>
                        役職手当
					</td>
					<td style="color:black;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						皆勤手当
					</td>
					<td style="color:black;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						勤務手当
					</td>
					<td style="color:black;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						歩合給
					</td>
					<td style="color:black;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						前月調整
					</td>
					<td style="color:black;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
                        技能手当
					</td>
				</tr>
				<tr style="border:black solid 1px;">
					<td class="salary" style="border:black solid 1px;">
						<br>
						<?php echo e((!empty($p_data->cd_salary_ap) ? number_format($p_data->cd_salary_ap): '')); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
                        <?php echo e((!empty($p_data->cd_salary_bp) ? number_format($p_data->cd_salary_bp): '')); ?>

                        <br>
                        <?php echo e((!empty($p_data->cd_salary_ar) ? number_format($p_data->cd_salary_ar): '')); ?>

					</td>
					<td class="salary" style="border:black solid 1px;"></br>
                        <br>
                        <?php echo e((!empty($p_data->cd_salary_at) ? number_format($p_data->cd_salary_at): '')); ?>

					</td style="border:black solid 1px;">
					<td class="salary" style="border:black solid 1px;"></br>
						<br>
                        <?php echo e((!empty($p_data->cd_salary_av) ? number_format($p_data->cd_salary_av): '')); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<br>
                        <?php echo e((!empty($p_data->cd_salary_ax) ? number_format($p_data->cd_salary_ax): '')); ?>

					</td>
					<td class="salary" style="border:black solid 1px;"></br>
						<br>
                        <?php echo e(!empty($p_data->cd_salary_bv) ? number_format($p_data->cd_salary_bv): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<br>
                        <?php echo e(!empty($p_data->cd_salary_bb) ? number_format($p_data->cd_salary_bb): ''); ?>

					</td>
				</tr>
			</thead>
			<tbody style="border:black solid 1px;">
				<tr style="background-color:#17a2b8;border:black solid 1px;">
					<td style="color:black;width:14%;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						休業手当 <br>休業補償
					</td>
					<td style="color:black;width:14%;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						補助手当<br>その他手当
					</td>
					<td style="color:black;width:14%;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
                        営業手当
					</td>
					<td style="color:black;width:14%;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						業務手当<br> 時間外手当
					</td>
					<td style="color:black;width:14%;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						通勤手当
					</td>
					<td style="color:black;width:14%;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						不就労控除
					</td>
					<td style="color:black;width:14%;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						総支給額
					</td>
				</tr>
				<tr style="border:black solid 1px;">
					<td class="salary" style="border:black solid 1px;">
                        <?php echo e(!empty($p_data->cd_salary_cg) ? number_format($p_data->cd_salary_cg): ''); ?>

						<br>
						<?php echo e(!empty($p_data->cd_salary_ch) ? number_format($p_data->cd_salary_ch): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<?php echo e(!empty($p_data->cd_salary_ay) ? number_format($p_data->cd_salary_ay): ''); ?>

						<br>
						<?php echo e(!empty($p_data->cd_salary_bf) ? number_format($p_data->cd_salary_bf): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<br>
                        <?php echo e(!empty($p_data->cd_salary_bh) ? number_format($p_data->cd_salary_bh): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<?php echo e(!empty($p_data->cd_salary_bj) ? number_format($p_data->cd_salary_bj): ''); ?>

						<br>
						<?php echo e(!empty($p_data->cd_salary_ck) ? number_format($p_data->cd_salary_ck): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<br>
						<?php echo e(!empty($p_data->cd_salary_cl) ? number_format($p_data->cd_salary_cl): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<br>
						<?php echo e(!empty($p_data->cd_salary_cm) ? number_format($p_data->cd_salary_cm): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<br>
						<?php echo e(!empty($p_data->cd_salary_cp) ? number_format($p_data->cd_salary_cp): ''); ?>

					</td>
				</tr>
			</tbody>
		</table>

		控除
		<table style="border:black solid 1px; margin-top:10px;" id="deduct_table" class="table">
			<thead style="border:black solid 1px;">
				<tr style="background-color:#17a2b8;border:black solid 1px;">
					<td style="color:black;width:14%;border:black solid 1px; text-align:center;" class="table-header2">
						健康保険
					</td>
					<td style="color:black;width:14%;border:black solid 1px; text-align:center;" class="table-header2">
						厚生年金
					</td>
					<td style="color:black;width:14%;border:black solid 1px; text-align:center;" class="table-header2">
						介護保険
					</td>
					<td style="color:black;width:14%;border:black solid 1px; text-align:center;" class="table-header2">
						雇用保険
					</td>
					<td style="color:black;width:14%;border:black solid 1px; text-align:center;" class="table-header2">
						保険料合計
					</td>
					<td style="color:black;width:14%;border:black solid 1px; text-align:center;" class="table-header2">
						所得税
					</td>
					<td style="color:black;width:14%;border:black solid 1px; text-align:center;" class="table-header2">
						住民税
					</td>
				</tr>
				<tr style="border:black solid 1px;">
					<td class="salary" style="border:black solid 1px;">
						<?php echo e(!empty($p_data->cd_salary_cq) ? number_format($p_data->cd_salary_cq): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<?php echo e(!empty($p_data->cd_salary_cu) ? number_format($p_data->cd_salary_cu): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<?php echo e(!empty($p_data->cd_salary_cs) ? number_format($p_data->cd_salary_cs): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<?php echo e(!empty($p_data->cd_salary_cy) ? number_format($p_data->cd_salary_cy): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<?php echo e(!empty($p_data->cd_salary_da) ? number_format($p_data->cd_salary_da): ''); ?>

					<td class="salary" style="border:black solid 1px;">
						<?php echo e((!empty($p_data->cd_salary_db) ? number_format($p_data->cd_salary_db): '')); ?>

					<td class="salary" style="border:black solid 1px;">
						<?php echo e((!empty($p_data->cd_salary_dc) ? number_format($p_data->cd_salary_dc): '')); ?>

					</td>
				</tr>
			</thead>
			<tbody>
				<tr style="background-color:#17a2b8;border:black solid 1px;">
					<td style="color:black;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						社保調整額<br>雇用調整額
					</td>
					<td style="color:black;border:black solid 1px;" class="table-header2">
						
					</td>
					<td style="color:black;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						社宅代
					</td>
					<td style="color:black;border:black solid 1px; vertical-align: middle;" class="table-header2">
						
					</td>
					<td style="color:black;border:black solid 1px; vertical-align: middle;" class="table-header2">
						
					</td>
					<td style="color:black;border:black solid 1px; vertical-align: middle;" class="table-header2">
						
					</td>
					<td style="color:black;border:black solid 1px; vertical-align: middle; text-align:center;" class="table-header2">
						控除計
					</td>
				</tr>
				<tr style="border:black solid 1px;">
					<td class="salary" style="border:black solid 1px;">
						<?php echo e(!empty($p_data->cd_salary_cr) ? number_format($p_data->cd_salary_cr): ''); ?><br>
                        <?php echo e(!empty($p_data->cd_salary_cz) ? number_format($p_data->cd_salary_cz): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						
					</td>
					<td class="salary" style="border:black solid 1px;">
						<br>
						<?php echo e(!empty($p_data->cd_salary_df) ? number_format($p_data->cd_salary_df): ''); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						
					</td>
					<td class="salary" style="border:black solid 1px;">
						
					</td>
					<td class="salary" style="border:black solid 1px;">
						
					</td>
					<td class="salary" style="border:black solid 1px;">
						<br>
						<?php echo e(!empty($p_data->cd_salary_eo) ? number_format($p_data->cd_salary_eo): ''); ?>

					</td>
				</tr>
			</tbody>
		</table>

		<table id="money_table" style="margin-top:10px;" class="table">
			<thead>
				<tr style="background-color:#17a2b8;border:black solid 1px;">
					<td style="color:black;width:14%;border:black solid 1px; text-align:center;" class="table-header2">
						その他調整
					</td>
					<td style="color:black;width:14%;border:black solid 1px;" class="table-header2">
						
					</td>
					<td style="color:black;width:14%;border:black solid 1px; text-align:center;" class="table-header2">
						年末調整
					</td>
					<td style="color:black;width:14%;border:black solid 1px; text-align:center;" class="table-header2">
						差引支給額
					</td>
					<td style="color:black;width:14%;border:black solid 1px;" class="table-header2">
						
					</td>
					<td style="color:black;width:14%;border:black solid 1px;" class="table-header2">
					</td>
					<td style="color:black;width:14%;border:black solid 1px; text-align:center;" class="table-header2">
						定額減税額
					</td>
				</tr>
			</thead>
			<tbody>
				<tr style="border:black solid 1px;">
					<td class="salary" style="border:black solid 1px;">
						<?php echo e((!empty($p_data->cd_salary_eq) ? number_format($p_data->cd_salary_eq): '')); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">

					</td>
					<td class="salary" style="border:black solid 1px;">
						<?php echo e((!empty($p_data->cd_salary_ep) ? number_format($p_data->cd_salary_ep): '')); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">
						<?php echo e((!empty($p_data->cd_salary_eu) ? number_format($p_data->cd_salary_eu): '')); ?>

					</td>
					<td class="salary" style="border:black solid 1px;">

					</td>
					<td class="salary" style="border:black solid 1px;">

					</td>
					<td class="salary" style="border:black solid 1px;">
						<?php echo e((!empty($p_data->cd_salary_ev) ? number_format($p_data->cd_salary_ev): '')); ?>

					</td>
				</tr>
			</tbody>
		</table>

		有給使用日数 <?php echo e(sprintf("%.2f", $p_data->cd_salary_ak)); ?> 有休残日数 <?php echo e(sprintf("%.2f", $p_data->cd_salary_al)); ?> 
		<table id="time_table" class="table" style="margin-top:10px;margin-right:10px;">
			<tr>
				<th style="border:black solid 1px;width:9%; text-align:center;" class="th2 width73">
					労働日数
				</th>
				<td style="border:black solid 1px;width:3%;" class="" align="right">
					<?php echo e((!empty($p_data->cd_salary_m) ? sprintf("%.2f", $p_data->cd_salary_m) : '')); ?>

				</td>
				<td style="width:1%;background-color:white;border-top:0px;border-bottom:0px;">&nbsp;</td>
				<th style="border:black solid 1px;" class="th2">&nbsp;</th>
				<th style="border:black solid 1px; text-align:center;" class="th2">
					欠勤
				</th>
				<th style="border:black solid 1px; text-align:center;" class="th2">
					不就労
				</th>
				<th style="border:black solid 1px; text-align:center;" class="th2">
					平日普通
				</th>
				<th style="border:black solid 1px; text-align:center;"class="th2">
					平日深夜
				</th>
				<th style="border:black solid 1px; text-align:center;" class="th2">
					休日普通
				</th>
				<th style="border:black solid 1px; text-align:center;" class="th2">
					休日深夜
				</th>
				<th style="border:black solid 1px; text-align:center;" class="th2">
					法休普通
				</th>
				<th style="border:black solid 1px; text-align:center;" class="th2">
					法休深夜
				</th>
			</tr>
			<tr>
				<th style="border:black solid 1px; text-align:center;" class="th2">
					出勤日数
				</th>
				<td style="border:black solid 1px;" align="right">
					<?php echo e((!empty($p_data->cd_salary_n) ? sprintf("%.2f", $p_data->cd_salary_n) : '')); ?>

				</td>
				<td style="background-color:white;border-top:0px;border-bottom:0px;">&nbsp;</td>
				<th style="border:black solid 1px; text-align:center;" class="th2">
					時間
				</th>
				<td style="border:black solid 1px;" align="right">
					<?php echo e((!empty($p_data->cd_salary_p) && sprintf("%.2f", $p_data->cd_salary_p)!='0.00' ? sprintf("%.2f", $p_data->cd_salary_p) : '')); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e((!empty($p_data->cd_salary_r) && sprintf("%.2f", $p_data->cd_salary_r)!='0.00'? sprintf("%.2f", $p_data->cd_salary_r) : '')); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e((!empty($p_data->cd_salary_t) && sprintf("%.2f", $p_data->cd_salary_t)!='0.00'? sprintf("%.2f", $p_data->cd_salary_t) : '')); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e((!empty($p_data->cd_salary_v) && sprintf("%.2f", $p_data->cd_salary_v)!='0.00'? sprintf("%.2f", $p_data->cd_salary_v) : '')); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e((!empty($p_data->cd_salary_x) && sprintf("%.2f", $p_data->cd_salary_x)!='0.00'? sprintf("%.2f", $p_data->cd_salary_x) : '')); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e((!empty($p_data->cd_salary_z) && sprintf("%.2f", $p_data->cd_salary_z)!='0.00'? sprintf("%.2f", $p_data->cd_salary_z) : '')); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e((!empty($p_data->cd_salary_ab) && sprintf("%.2f", $p_data->cd_salary_ab)!='0.00'? sprintf("%.2f", $p_data->cd_salary_ab) : '')); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e((!empty($p_data->cd_salary_ad) && sprintf("%.2f", $p_data->cd_salary_ad)!='0.00'? sprintf("%.2f", $p_data->cd_salary_ad) : '')); ?>

				</td>
			</tr>
			<tr>
				<th style="border:black solid 1px; text-align:center;" class="th2">	
					出勤時数
				</th>
				<td style="border:black solid 1px;" align="right">
					<?php echo e((!empty($p_data->cd_salary_o) ? sprintf("%.2f", $p_data->cd_salary_o) : '')); ?>

				</td>
				<td style="background-color:white;border-top:0px;border-bottom:0px;">&nbsp;</td>
				<th style="border:black solid 1px; text-align:center;" class="th2">					
					金額
				</th>
				<td style="border:black solid 1px;" align="right">
					<?php echo e(!empty((int)$p_data->cd_salary_p*(int)$p_data->cd_salary_q) ? number_format(floor((float)$p_data->cd_salary_p*(float)$p_data->cd_salary_q)) : ''); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e(!empty((float)$p_data->cd_salary_r*(float)$p_data->cd_salary_s) ? number_format(floor((float)$p_data->cd_salary_r*(float)$p_data->cd_salary_s)) : ''); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e(!empty((float)$p_data->cd_salary_t*(float)$p_data->cd_salary_u) ? number_format(floor((float)$p_data->cd_salary_t*(float)$p_data->cd_salary_u)) : ''); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e(!empty((float)$p_data->cd_salary_v*(float)$p_data->cd_salary_w) ? number_format(floor((float)$p_data->cd_salary_v*(float)$p_data->cd_salary_w)) : ''); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e(!empty((float)$p_data->cd_salary_x*(float)$p_data->cd_salary_y) ? number_format(floor((float)$p_data->cd_salary_x*(float)$p_data->cd_salary_y)) : ''); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e(!empty((float)$p_data->cd_salary_z*(float)$p_data->cd_salary_aa) ? number_format(floor((float)$p_data->cd_salary_z*(float)$p_data->cd_salary_aa)) : ''); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e(!empty((float)$p_data->cd_salary_ab*(float)$p_data->cd_salary_ac) ? number_format(floor((float)$p_data->cd_salary_ab*(float)$p_data->cd_salary_ac)) : ''); ?>

				</td>
				<td style="border:black solid 1px;" align="right">
					<?php echo e(!empty((float)$p_data->cd_salary_ad*(float)$p_data->cd_salary_ae) ? number_format(floor((float)$p_data->cd_salary_ad*(float)$p_data->cd_salary_ae)) : ''); ?>

				</td>
			</tr>
		</table>
		<?php $route_name = request()->route()->getname(); ?>
        <?php if(strpos($route_name, 'portal_site.') === false): ?>
		<table class="table">
			<tr style="border:none">
				<td colspan="100" style="border:none; padding-right:0px"  align="right"><?php echo e($listCompany[$p_data->cd_salary_e]??""); ?></td>
			</tr>
		</table>
		<?php endif; ?>
	</div>
</div>
<?php endif; ?><?php /**PATH E:\TOOLS\laragon\www\control_management\resources\views/Staff/Paylist/Includes/payslip-modal-content-admin.blade.php ENDPATH**/ ?>