<!-- Start: Modal - Staff Search Delete -->
<?php $__env->startPush('plugins-css'); ?>
	<style type="text/css">
		#payslip-format-modal .btn{
			padding: 10px 20px !important;
		}
		.swal-overlay {
			z-index: 999999;
		}
		.bs-tooltip-right.show{
			z-index: 9999999;
		}
		.tooltip-inner {
			min-width: 330px; /* the minimum width */
			text-align: left !important;
			background-color: #274799 !important;
			color: white !important;
		}
		.tooltip-inner .head {
			color: white !important;
			margin-bottom: 15px !important;
		}
	</style>
<?php $__env->stopPush(); ?>
<div class="tg-modal tg-modal_large box--shadow" style="" id="payslip-format-modal">
	<div class="container tg-modal__container">
		<div id="modal_content">

			<div class="row">
		        <div class="col-md">
		            <div class="tg-modal__heading">
		            	<h3 class="color-secondary">
							<i class="fas fa-dot-circle mr-1"></i>
							<strong>フォーマット管理</strong>
						</h3>
		            </div>
		        </div>
		    </div>

		    <div class="row">
        		<div class="col-md">
                    <div class="d-flex justify-content-between">
                        <!-- New dropdown box -->
                        <div class="col-5 mb-3 pl-0">
                            <label class="mb-2">所属カテゴリ</label>
                            <div class="filter-group-container" data-self_id="format-playslip" data-multiple_mode="false">
                                <select id="filter-group-format-box" class="select2-select filter-group-format">
                                    <option>全て</option>
                                </select>
                                <input type="hidden" class="admin_id" value=""/>
                                <input type="hidden" class="group_id" id="filter-group-format-id" value=""/>
                            </div>			
                        </div>

                        <div class="col-5 pl-5 mt-4 text-right">
                            <button class="btn btn-primary w-90" id="payslip_format_create">新規フォーマット追加</button>
                        </div>
                    </div>

					<div class="table-responsive default-scrollbar">
				        <table class="table table-bordered" id="format-table">
				            <thead class="thead-cyan">
				                <tr>
				                		<th scope="col" width="1%"></th>
									<th scope="col" width="5%">ID</th>
				                    <th scope="col" width="25%">フォーマット名</th>
				                    <th scope="col">作成者</th>
				                    <th scope="col">作成日</th>
				                    <th scope="col" width="45%" class="text-center">アクション</th>
				                </tr>
				            </thead>
				            <tbody id="tbodysort">
								<?php if(!empty($modal_format)): ?>
									<?php $__currentLoopData = $modal_format; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $format): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
										<tr data-sort="<?php echo e($format->sort); ?>" data-id="<?php echo e($format->id); ?>">
											<td class="pt-4" >
                                                <i class="fas fa-sort fa-lg color-coal"></i>
											</td>
											<td><?php echo e($format->format_id); ?></td>
											<td><?php echo e($format->format_name); ?></td>
                                           
                                            <?php if($format->is_default !=1): ?>
                                                <td><?php echo e($format->admin_name); ?></td>
                                                <td><?php echo e(date('Y-m-d', strtotime($format->create_date))); ?></td>
                                            <?php else: ?>
                                                <td></td>
                                                <td></td>
                                            <?php endif; ?>
											<td class="text-right" >
												<button class="btn btn-primary mb-2 mb-sm-0 format_edit_modal_btn" data-type='readonly' value="<?php echo e($format->id); ?>">閲覧</button>
                                                <?php if($format->is_default !=1): ?>
                                                    <button <?php echo e($format->has_use > 0 ? "disabled":""); ?> data-type='edit' class="btn btn-primary mb-2 mb-sm-0 format_edit_modal_btn" value="<?php echo e($format->id); ?>">編集</button>
                                                    <button <?php echo e($format->has_use > 0 ? "disabled":""); ?> class="btn btn-danger mb-2 mb-sm-0 format_delete_modal_btn" value="<?php echo e($format->id); ?>">削除</button>
                                                <?php endif; ?>
												<button class="btn btn-primary mb-2 mb-sm-0 format_download_csv_btn" value="<?php echo e($format->id); ?>">Csvダウンロード</button>
                                                <?php if($format->has_use > 0 ): ?>
                                                    <button class="mb-2 mb-sm-0 btn_copy_format" value="<?php echo e($format->id); ?>">+</button>
                                                <?php endif; ?>
											</td>
										</tr>
									<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
								<?php else: ?>
									<tr><th colspan='6' class='text-center'>テーブル内のデータなし</th></tr>
								<?php endif; ?>
				            </tbody>
				        </table>
				    </div>
					<div class="d-flex justify-content-between">
						<p style='color:red'>一度使用したフォーマットの編集や削除はできません。</p>
						<button class="btn btn-primary w-90" id="create_csv_guideline">CVSの入力方法</button>
                    </div>
        		</div>
        	</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	var data_serialized_before = null;
	$('#tbodysort').sortable({
		items: 'tr:not(:first)',
		start	: function () {
			$('#tbodysort tr').each(function(){
				$(this).data('sort',$(this).attr('data-sort'));
				$(this).data('id',$(this).attr('data-id'));

				$(this).removeAttr('data-sort');
				$(this).removeAttr('data-id');
			});
		},
		change : function (event, ui) {
		},
		update : function(event, ui) {
			var format_id = {};
			$('#tbodysort tr').each(function(index){
				if ($('#tbodysort tr:eq(' + (index + 1) + ')').length != 0) {
					var current_sort 	= $(this).data('sort');
					var next_sort 		= $('#tbodysort tr:eq(' + (index + 1) + ')').data('sort');
					if (current_sort < next_sort) {
						$(this).data('sort',next_sort);
						$('#tbodysort tr:eq(' + (index + 1) + ')').data('sort',current_sort);
					}
				}
				format_id[index] = {
					'sort' 	        : index,
					'id' 	        : $(this).data('id')
				}
			});
			
			$.ajax({
				url 	: 'save-payslip-format-sorting',
				type	: 'POST',
				headers: {
						'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
						},
				data	: {
					format_list : format_id
				}
			})
			.done(function (response) {
			
			})
			.fail(function () {
				swal('エラー','','error');
			});
		}
	});

	$(function () {
		$('#create_csv_guideline').off().on('click', function (e) {
			$('#create-csv-guidline-modal').iziModal('open', { zindex: 999999 });
		});

		$('#payslip_format_create').off().on('click', function (e) {
			$('#btn_save_payslip_format').attr('disabled', true);
			$('#payslip-format-create-modal').iziModal('open', { zindex: 99999 });
			$('#record_id').val('');
			$('#format_id').val('');
			$('#format_name').val('');
			$('#payslip-format-create-modal .title-setting').each(function(i) {
				$(this).val('');
			});

			//$('#tooltipshow').tooltip('show');   

			var section_1_data =  section_2_data = section_3_data = "";
			var arr_data_1  = [
				'基本給',
				'役員報酬',
				'皆勤手当',
				'勤務手当',
				'歩合給',
				'前月調整',
				'技能手当',
				'休業手当',
				'補助手当',
				'その他手当',
				'通勤手当',
				'時間外手当',
				'不就労控除',
				'総支給額'
			];

			var arr_data_2  = [
				'健康保険',
				'厚生年金',
				'介護保険',
				'雇用保険',
				'保険料合計',
				'所得税',
				'住民税',
				'社保調整額',
				'雇用調整額',
				'',
				'社宅代',
				'',
				'',
				'控除計'
			];

			var arr_data_3  = [
				'その他調整',
				'',
				'年末調整',
				'差引支給額',
				'',
				'',
				''
			];

			for(var i=0; i< 14; i++){
				section_1_data +=`
					<div class="item">
						<div class="title">
							<input type="text" class="form-control title-setting clearable" value="${arr_data_1[i]}">
							
						</div>
						<div class="value"></div>
					</div>
				`;
			}

			for(var i=0; i< 14; i++){
				section_2_data +=`
					<div class="item">
						<div class="title">
							<input type="text" class="form-control title-setting clearable" value="${arr_data_2[i]}">
							
						</div>
						<div class="value"></div>
					</div>
				`;
			}

			for(var i=0; i< 7; i++){
				section_3_data +=`
					<div class="item">
						<div class="title">
							<input type="text" class="form-control title-setting clearable" value="${arr_data_3[i]}">
							
						</div>
						<div class="value"></div>
					</div>
				`;
			}
			$(".section-4 input[name=section4_1]").val('労働日数');
			$(".section-4 input[name=section4_2]").val('出勤日数');
			$(".section-4 input[name=section4_3]").val('出勤時数');
			$(".section-4 input[name=section4_4]").val('欠勤');
			$(".section-4 input[name=section4_5]").val('不就労');
			$(".section-4 input[name=section4_6]").val('平日普通');
			$(".section-4 input[name=section4_7]").val('平日深夜');
			$(".section-4 input[name=section4_8]").val('休日普通');
			$(".section-4 input[name=section4_9]").val('休日深夜');
			$(".section-4 input[name=section4_10]").val('法休普通');
			$(".section-4 input[name=section4_11]").val('法休深夜');

			$('#payslip-format-create-modal .section-1 .section-wrapper').html(section_1_data);
			$('#payslip-format-create-modal .section-2 .section-wrapper').html(section_2_data);
			$('#payslip-format-create-modal .section-3 .section-wrapper').html(section_3_data);
			$('.addRow').attr('data-modal','create');
			$('.reduceRow').attr('data-modal','create');
			$('#btn_save_payslip_format').text('登録');
			setIconClearAllInput();
			// Enable butto submit
			setStatusSubmit('enable');
		});

		$('#payslip-format-create-modal #format_id, #payslip-format-create-modal #format_name').on('input', function() {
			_format_id = $('#format_id').val() || 0;
			_format_name = $('#format_name').val() || '';

			if(_format_id != 0 && _format_name != ''){
				$('#btn_save_payslip_format').attr('disabled', false);
			}else{
				$('#btn_save_payslip_format').attr('disabled', true);
			}
		});

		$('#btn_save_payslip_format').off().on('click', function (e) {
			var _data ={};
			_data.id = $('#record_id').val() || 0;
			_data.format_id = $('#format_id').val() || 0;
			_data.format_name = $('#format_name').val() || '';
			_data.current_format_id = $('#current_format_id').val() || 0;
			var arr = [];
			$( "#payslip-format-create-modal .title-setting" ).each(function( index ) {
				arr.push({
					section: $(this).closest('.section').data('section_id') || 4, 
					index:  index+1,
					value: $(this).val()

				});
			});
			_data.items = arr;
			
			$.ajax({
				url: "store-payslip-format",
				headers: {
					'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
				},
				method: "POST",
				data: {
					format_data: _data
				},
				
				beforeSend: function() {
					$(body).append('<div class="email-loader"><div class="card p-4 text-center"><div class="card-body"><div class="spinner-border mb-4" role="status"><span class="sr-only">Loading...</span></div><h4 class="mt-1">Loading...</h4></div></div></div>')
					//$('#payslip-format-create-modal .iziModal-button-close').click();
				},
				success: function(response) {
					$(".email-loader").remove()
					swal({
						title: "登録が完了しました",
						icon: "success",
						buttons: {
							confirm: {
								text: "Ok",
								value: true,
							},
							cancel: false,
						}
					}).then(() => {
						location.reload(true);
					});
					return true
				},
				error: function(err) {
					$(".email-loader").remove();
					var message = err.responseJSON.message === 'フォーマットIDが存在します' ? err.responseJSON.message : '';
					swal('エラー', message, 'error').then(() => {});
				}
			});
		});

		$('#payslip-format-modal .filter-group-format').on('change', function() {
			$.ajax({
					url: 'get-payslip-format-by-group',
					headers: {
						'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
					},
					type: 'POST',
					data: {
						'group_id': $('#filter-group-format-id').val()
					}
				})
				.done(function(response) {
					$('#payslip-format-modal #format-table tbody').html(response);
					// DELETE BUTTON OF FORMAT IN MODAL
					$('#payslip-format-modal #format-table tbody .format_delete_modal_btn').on('click', function() {
						$('#format_delete_modal').iziModal('open');
						$('#delete_format_id').val(this.value);
					})
					delete_payslip_format();
					edit_payslip_format();
					download_payslip_format();
				})
				.fail(function() {
					swal('エラー', '', 'error');
				});
		});

		$(".addRow").off().on('click', function (e) {
			var modal = $(this).attr('data-modal');
			var name_number = randNumber();
			var html_addrow ="";
			var max_cell = $(this).data('count_cell') + 14;
			var total_cell =  $(this).closest('.section').find('.section-wrapper .item').length;
			var section_id =  $(this).closest('.section').attr('data-section_id');
			if(total_cell <= max_cell)
			{
				for(var i=1;i<=7;i++){
					html_addrow += "<div class='item'><div class='title'><input type='text' name='section"+section_id+"_"+name_number+"' class='form-control title-setting clearable'></div><div class='value'></div></div>";
				}
				$(this).closest('.section').find('.section-wrapper').append(html_addrow);
			}
			setIconClearAllInput();
			if(modal==='edit'){
				trackingRenderInput();
			}
		});

		$(".reduceRow").off().on('click', function (e) {
			var modal = $(this).attr('data-modal');
			var total_cell =  $(this).closest('.section').find('.section-wrapper .item').length;
			var max_cell = $(this).data('count_cell');
			if(total_cell > max_cell){
				for(var i=0;i<7;i++){
					$(this).closest('.section').find(`.section-wrapper .item:nth-child(${total_cell-i})`).remove();
				}
			}
			setIconClearAllInput();
			if(modal==='edit'){
				trackingRenderInput();
			}
		});

		$(".section-3-button-action .addRow").off().on('click', function (e) {
			var modal = $(this).attr('data-modal');
			var name_number = randNumber();
			var html_addrow ="";
			var max_cell = $(this).data('count_cell') + 14;
			var total_cell =  $('#payslip-format-create-modal .section-3').find('.section-wrapper .item').length;
			var section_id =  $('#payslip-format-create-modal .section-3').attr('data-section_id');
			console.log(max_cell, total_cell)

			if(total_cell <= max_cell)
			{
				for(var i=1;i<=7;i++){
					html_addrow += "<div class='item'><div class='title'><input type='text' name='section"+section_id+"_"+name_number+"' class='form-control title-setting clearable'></div><div class='value'></div></div>";
				}
				$('#payslip-format-create-modal .section-3').find('.section-wrapper').append(html_addrow);
			}
			setIconClearAllInput();
			if(modal==='edit'){
				trackingRenderInput();
			}
		});

		$(".section-3-button-action .reduceRow").off().on('click', function (e) {
			var modal = $(this).attr('data-modal');
			var total_cell =  $('#payslip-format-create-modal .section-3').find('.section-wrapper .item').length;
			var max_cell = $(this).data('count_cell');
			if(total_cell > max_cell){
				for(var i=0;i<7;i++){
					$('#payslip-format-create-modal .section-3').find(`.section-wrapper .item:nth-child(${total_cell-i})`).remove();
				}
			}
			setIconClearAllInput();
			if(modal==='edit'){
				trackingRenderInput();
			}
		});

		copyPayslipFormat();
		delete_payslip_format();
		edit_payslip_format();
		download_payslip_format();
	});

	function randNumber(){
		return parseInt(Math.random() * (500 - 50) + 50);
	}

	function delete_payslip_format(param) {  
		$(document).on('click', '.format_delete_modal_btn', function(event) {
			var record_id = this.value;

			$.ajax({
				url: 'check-payslip-format-used',
				headers: {
					'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
				},
				type: 'POST',
				data: {
					'id': record_id
				}
			})
			.done(function(response) {
				if(response == 1){
					$('.delete_message').html('このフォーマットは既に使用しているため削除できません。');
					$('#format_delete_modal .btn-light').hide();
					$('#format_delete_modal .btn-ok').show();
					$('#format_delete_modal .btn-submit').attr('style', 'display: none !important');
					$('.btn-ok').off().on('click', function() {
						$('#format_delete_modal').iziModal('close');
					});
				}else{
					$('.delete_message').html('削除してよろしいですか？');
					$('#format_delete_modal .btn-light').show();
					$('#format_delete_modal .btn-ok').hide();
					$('#format_delete_modal .btn-submit').show();
				}
				$('#format_delete_modal').iziModal('open');
				$('#delete_format_id').val(record_id);

			})
			.fail(function() {
				swal('エラー', '', 'error');
			});
		});
	}

	function edit_payslip_format(param) {  
		$(document).on('click', '.format_edit_modal_btn', function(event) {
			var type = $(this).attr('data-type');
			$.ajax({
				url: "edit-payslip-format",
				headers: {
					'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
				},
				method: "GET",
				data: {
					id: this.value
				},
				beforeSend: function() {
					$(body).append('<div class="email-loader"><div class="card p-4 text-center"><div class="card-body"><div class="spinner-border mb-4" role="status"><span class="sr-only">Loading...</span></div><h4 class="mt-1">Loading...</h4></div></div></div>')
				},
				success: function(response) {
					$(".email-loader").remove()
					$('#payslip-format-create-modal').iziModal('open', { zindex: 99999 });
					$('#record_id').val(response.format_data.id);
					$('#format_id').val(response.format_data.format_id);
					$('#current_format_id').val(response.format_data.format_id);
					$('#format_name').val(response.format_data.format_name);
					var disabled = type === 'readonly' ? "disabled" : "";
					var header_data = response.format_data.value;
					var section_1_data = "";
					var section_2_data = "";
					var section_3_data = "";
					var section_4_start = 0;
					for(var i=0; i< header_data.length; i++){
						var title = '';
						if(header_data[i].value !== null && header_data[i].value !== '') {
							title = header_data[i].value;
						}
						if(header_data[i].section == 1){
							section_1_data +=`
								<div class="item">
									<div class="title">
										<input type="text" data-exist='true' name="section1_${i}" ${disabled} class="form-control title-setting clearable" value="${title}">
									</div>
									<div class="value"></div>
								</div>
							`;
						}
						if(header_data[i].section == 2){
							section_2_data +=`
								<div class="item">
									<div class="title">
										<input type="text" data-exist='true' name="section2_${i}" ${disabled} class="form-control title-setting clearable" value="${title}">
									</div>
									<div class="value"></div>
								</div>
							`;
						}
						if(header_data[i].section == 3){
							section_3_data +=`
								<div class="item">
									<div class="title">
										<input type="text" data-exist='true' name="section3_${i}" ${disabled} class="form-control title-setting clearable" value="${title}">
									</div>
									<div class="value"></div>
								</div>
							`;
							section_4_start = i+1;
						}
					}
					
					$('#payslip-format-create-modal .section-1 .section-wrapper').html(section_1_data);
					$('#payslip-format-create-modal .section-2 .section-wrapper').html(section_2_data);
					$('#payslip-format-create-modal .section-3 .section-wrapper').html(section_3_data);
					$('#payslip-format-create-modal .section-4 .title-setting').each(function(i) {
						var title = '';
						var idx = section_4_start + i;
						if(header_data[idx].value !== null && header_data[idx].value !== '') {
							title = header_data[idx].value;
						}
						$(this).val(title);
					});
					$('.addRow').attr('data-modal','edit');
					$('.reduceRow').attr('data-modal','edit');
					$('#btn_save_payslip_format').text('保存');
					data_serialized_before = $('#frm_payslip_format').serialize();
					// Nếu template mặc định không cho chỉnh sửa nội dung
					setStatusSubmit(disabled);
					// Show clear button
					setIconClearAllInput();
					// Theo dõi giá trị nhập vào input
					trackingChangeValueInput();
				},
				error: function(err) {
					$(".email-loader").remove()
					swal('エラー', '', 'error').then(() => {
						location.reload(true);
					});
				}
			});
		})
	}
	 var isLoadingDownload = false;
	function download_payslip_format(param) {  
		$(document).on('click', '.format_download_csv_btn', function(event) {
			if(!isLoadingDownload){
				isLoadingDownload = true;
				$.ajax({
					url: "download-payslip-format",
					headers: {
						'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
					},
					method: "GET",
					data: {
						id: this.value
					},
					beforeSend: function() {
						$(body).append('<div class="email-loader"><div class="card p-4 text-center"><div class="card-body"><div class="spinner-border mb-4" role="status"><span class="sr-only">Loading...</span></div><h4 class="mt-1">Loading...</h4></div></div></div>');
					},
					success: function(data) {
						isLoadingDownload = false;
						$(".email-loader").remove()
						var $a = $("<a>")
						$a.attr("href", data.file)
						$("body").append($a)
						$a.attr("download", data.filename)
						$a[0].click()
						$a.remove()
					},
					error: function(err) {
						$(".email-loader").remove()
						swal('エラー', '', 'error').then(() => {
							location.reload(true);
						});
					}
				});
			}
		})
	}

	var isLoadingCopy = false;
	function copyPayslipFormat(){
		$(document).on('click', '.btn_copy_format', function(event) {
			event.preventDefault();
			let format_id = parseInt($(this).val());
			if(format_id > 0){
				if(!isLoadingCopy){
					isLoadingCopy = true;
					$.ajax({
						url: "copy-payslip-format",
						headers: {
							'X-CSRF-TOKEN': $('meta[name="csrsf-token"]').attr('content')
						},
						method: "POST",
						data: {
							format_id: format_id
						},
						beforeSend: function() {
							$(body).append('<div class="email-loader"><div class="card p-4 text-center"><div class="card-body"><div class="spinner-border mb-4" role="status"><span class="sr-only">Loading...</span></div><h4 class="mt-1">Loading...</h4></div></div></div>');
						},
						success: function(resp) {
							isLoadingCopy = false;
							getListPaySlipFormat();
							setTimeout(() => {
								$(".email-loader").remove();
							}, 1000);
						},
						error: function(err) {
							$(".email-loader").remove()
							swal('エラー', '', 'error').then(() => {
								location.reload(true);
							});
						}
					});
				}
			}
		});
	}

	var isLoadingGetList = false;
	function getListPaySlipFormat(){
		if(!isLoadingGetList){
			isLoadingGetList = true;
			$.ajax({
				url: "get-list-payslip-format",
				headers: {
					'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
				},
				method: "POST",
				data: {},
				beforeSend: function() {
					
				},
				success: function(resp) {
					isLoadingGetList = false;
					$('#tbodysort').html(resp.html);
				},
				error: function(err) {
					swal('エラー', '', 'error').then(() => {
						location.reload(true);
					});
				}
			});
		}
	}

	function togIconClear(v){return v?'addClass':'removeClass';} 

	function setIconClearAllInput(){
		$(document).on('input', '.clearable', function(){
			$(this)[togIconClear(this.value)]('x');
		}).on('mousemove', '.x', function( e ){
			$(this)[togIconClear(this.offsetWidth-18 < e.clientX-this.getBoundingClientRect().left)]('onX');   
		}).on('click', '.onX', function(){
			$(this).val('');
			trackingRenderInput();
		});

		// $('.clearable').each(function(i, obj) {
		//     $(this).addClass('x onX');
		// });

		// $(document).on('click', '.onX', function(){
		//     $(this).val('');
		//     trackingRenderInput();
		// });
	}

	function setStatusSubmit(status){
		if(status === 'disabled'){
			$('.title-setting').removeClass('clearable x onX');
			$('input').attr('disabled',true);
			$('.addrow-wrapper').hide();
			$('#btn_save_payslip_format').hide();
		}else{
			$('.title-setting').addClass('clearable x onX');
			$('input').attr('disabled',false);
			$('.addrow-wrapper').show();
			$('#btn_save_payslip_format').show();
		}
	}

	// Theo dõi sự thay đổi của input
	function trackingChangeValueInput(){
		$('#frm_payslip_format').each(function(){
			$(this).data('serialized', $(this).serialize());
		}).on('change input', function(){
			$(this).find('#btn_save_payslip_format').attr('disabled', $(this).serialize() ===  $(this).data('serialized'));
		}).find('#btn_save_payslip_format').attr('disabled', true);
	}

	function trackingRenderInput(){
		$('#btn_save_payslip_format').attr('disabled', $('#frm_payslip_format').serialize() === data_serialized_before);
	}
</script>
<?php /**PATH E:\TOOLS\laragon\www\control_management\resources\views/Staff/Paylist/Includes/payslip-format-modal.blade.php ENDPATH**/ ?>