[2024-08-06 11:16:25] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-08-06 11:16:25] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(156): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(140): Illuminate\Foundation\Http\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(110): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#11 E:\TOOLS\laragon\www\control_management\public\index.php(54): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#12 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(156): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#11 E:\\TOOLS\\laragon\\www\\control_management\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#12 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-08-06 11:16:27] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-08-06 11:16:27] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(156): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(140): Illuminate\Foundation\Http\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(110): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#11 E:\TOOLS\laragon\www\control_management\public\index.php(54): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#12 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(156): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#11 E:\\TOOLS\\laragon\\www\\control_management\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#12 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-08-06 11:20:37] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-08-06 11:20:37] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(156): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(140): Illuminate\Foundation\Http\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(110): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#11 E:\TOOLS\laragon\www\control_management\public\index.php(54): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#12 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(156): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#11 E:\\TOOLS\\laragon\\www\\control_management\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#12 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-08-06 11:20:39] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-08-06 11:20:39] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(156): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(140): Illuminate\Foundation\Http\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(110): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#11 E:\TOOLS\laragon\www\control_management\public\index.php(54): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#12 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(156): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#11 E:\\TOOLS\\laragon\\www\\control_management\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#12 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-08-12 15:33:02] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-08-12 15:33:02] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-08-15 10:45:44] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [sent_mail_log] is not defined. at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:192)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('sent_mail_log')
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('sent_mail_log')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(87): Illuminate\\Log\\LogManager->driver('sent_mail_log')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(261): Illuminate\\Log\\LogManager->channel('sent_mail_log')
#4 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Controllers\\Admin\\AdminController.php(3587): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\AdminController->sendMultipleEmailsInvitation(Object(Illuminate\\Http\\Request))
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('sendMultipleEma...', Array)
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\AdminController), 'sendMultipleEma...')
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#10 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckRoleAdmin.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckRoleAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckOriginSite.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckOriginSite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 500, '1')
#19 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckPortal.php(123): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPortal->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 E:\\TOOLS\\laragon\\www\\control_management\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 E:\\TOOLS\\laragon\\www\\control_management\\server.php(21): require_once('E:\\\\TOOLS\\\\larago...')
#55 {main}
"} 
[2024-08-15 10:45:44] laravel.INFO: array (
  'Sent Mail At' => '2024-08-15 10:45:44',
  'Sent Mail Duration Time' => '3.0994415283203E-6 seconds',
  'Sent Mail Count' => '選択：1件',
  'Status' => 'Sent Mail Selected',
)  
[2024-08-15 10:46:44] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [sent_mail_log] is not defined. at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:192)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('sent_mail_log')
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('sent_mail_log')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(87): Illuminate\\Log\\LogManager->driver('sent_mail_log')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(261): Illuminate\\Log\\LogManager->channel('sent_mail_log')
#4 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Controllers\\Admin\\AdminController.php(3563): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\AdminController->sendMultipleEmailsInvitation(Object(Illuminate\\Http\\Request))
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('sendMultipleEma...', Array)
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\AdminController), 'sendMultipleEma...')
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#10 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckRoleAdmin.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckRoleAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckOriginSite.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckOriginSite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 500, '1')
#19 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckPortal.php(123): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPortal->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 E:\\TOOLS\\laragon\\www\\control_management\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 E:\\TOOLS\\laragon\\www\\control_management\\server.php(21): require_once('E:\\\\TOOLS\\\\larago...')
#55 {main}
"} 
[2024-08-15 10:46:44] laravel.INFO: array (
  'Sent Mail At' => '2024-08-15 10:46:44',
  'Sent Mail Duration Time' => '0.063043832778931 seconds',
  'Sent Mail Count' => '選択：1件',
  'Status' => 'Sent All Mail',
)  
[2024-08-15 10:47:26] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [sent_mail_log] is not defined. at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:192)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('sent_mail_log')
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('sent_mail_log')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(87): Illuminate\\Log\\LogManager->driver('sent_mail_log')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(261): Illuminate\\Log\\LogManager->channel('sent_mail_log')
#4 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Controllers\\Admin\\AdminController.php(3563): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\AdminController->sendMultipleEmailsInvitation(Object(Illuminate\\Http\\Request))
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('sendMultipleEma...', Array)
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\AdminController), 'sendMultipleEma...')
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#10 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckRoleAdmin.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckRoleAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckOriginSite.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckOriginSite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 500, '1')
#19 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckPortal.php(123): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPortal->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 E:\\TOOLS\\laragon\\www\\control_management\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 E:\\TOOLS\\laragon\\www\\control_management\\server.php(21): require_once('E:\\\\TOOLS\\\\larago...')
#55 {main}
"} 
[2024-08-15 10:47:26] laravel.INFO: array (
  'Sent Mail At' => '2024-08-15 10:47:26',
  'Sent Mail Duration Time' => '0.034063100814819 seconds',
  'Sent Mail Count' => '選択：1件',
  'Status' => 'Sent All Mail',
)  
[2024-08-15 10:48:53] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [sent_mail_log] is not defined. at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:192)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('sent_mail_log')
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('sent_mail_log')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(87): Illuminate\\Log\\LogManager->driver('sent_mail_log')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(261): Illuminate\\Log\\LogManager->channel('sent_mail_log')
#4 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Controllers\\Admin\\AdminController.php(3587): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\AdminController->sendMultipleEmailsInvitation(Object(Illuminate\\Http\\Request))
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('sendMultipleEma...', Array)
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\AdminController), 'sendMultipleEma...')
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#10 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckRoleAdmin.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckRoleAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckOriginSite.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckOriginSite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 500, '1')
#19 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckPortal.php(123): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPortal->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 E:\\TOOLS\\laragon\\www\\control_management\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 E:\\TOOLS\\laragon\\www\\control_management\\server.php(21): require_once('E:\\\\TOOLS\\\\larago...')
#55 {main}
"} 
[2024-08-15 10:48:53] laravel.INFO: array (
  'Sent Mail At' => '2024-08-15 10:48:53',
  'Sent Mail Duration Time' => '1.9073486328125E-6 seconds',
  'Sent Mail Count' => '選択：1件',
  'Status' => 'Sent Mail Selected',
)  
[2024-08-21 15:24:07] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-08-21 15:24:07] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-10-16 18:25:36] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-10-16 18:25:36] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(156): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(140): Illuminate\Foundation\Http\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(110): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#11 E:\TOOLS\laragon\www\control_management\public\index.php(54): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#12 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(156): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#11 E:\\TOOLS\\laragon\\www\\control_management\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#12 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-11-27 13:01:29] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-11-27 13:01:29] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-11-27 13:02:47] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-11-27 13:02:47] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-11-27 13:03:51] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-11-27 13:03:51] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-11-27 13:11:34] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-11-27 13:11:34] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-11-27 13:21:38] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-11-27 13:21:38] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(576): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-03-12 11:53:39] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-03-12 11:53:39] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-02 13:21:50] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-02 13:21:50] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-02 13:22:03] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-02 13:22:03] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-03 17:41:06] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-03 17:41:06] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\check_leave_early_data.php(6): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\check_leave_early_data.php(6): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-04 13:32:18] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-04 13:32:18] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\debug_attendance_1370.php(7): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\debug_attendance_1370.php(7): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-04 13:35:53] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-04 13:35:53] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-04 13:46:05] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-04 13:46:05] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-09 11:58:34] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-09 11:58:34] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\test_tax_query.php(11): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\test_tax_query.php(11): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-09 11:58:52] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-09 11:58:52] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-10 17:30:29] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-10 17:30:29] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-10 17:31:33] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-10 17:31:33] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\check_15mins_setting.php(7): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\check_15mins_setting.php(7): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-10 17:38:08] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-10 17:38:08] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-10 18:10:44] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-10 18:10:44] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\test_rounding.php(5): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\test_rounding.php(5): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-11 16:11:44] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-11 16:11:44] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-11 16:18:02] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-11 16:18:02] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-11 16:28:47] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-11 16:28:47] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-18 11:13:02] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-18 11:13:02] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-18 11:13:43] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-18 11:13:43] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-18 11:18:09] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-18 11:18:09] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-18 13:48:22] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-18 13:48:22] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-18 13:56:31] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-18 13:56:31] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\debug_attendance.php(9): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\debug_attendance.php(9): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-18 18:08:46] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-18 18:08:46] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(156): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(140): Illuminate\Foundation\Http\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(110): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#11 E:\TOOLS\laragon\www\control_management\debug_attendance.php(13): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#12 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(156): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#11 E:\\TOOLS\\laragon\\www\\control_management\\debug_attendance.php(13): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#12 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-28 15:15:17] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(124): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-28 15:15:17] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(37): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-28 15:15:56] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(124): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-28 15:15:56] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(37): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-28 15:16:51] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-28 15:16:51] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-28 15:17:38] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-28 15:17:38] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-28 15:17:53] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:109)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(119): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\app\\Exceptions\\Handler.php(37): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(92): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(134): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\Debug\\Exception\\FatalErrorException))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2025-07-28 15:17:53] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1277
Stack trace:
#0 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\TOOLS\\larago...', 1277)
#1 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(571): include('E:\\TOOLS\\larago...')
#2 E:\TOOLS\laragon\www\control_management\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('E:\\TOOLS\\larago...')
#3 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Support\helpers.php(109): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): collect(Array)
#5 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#6 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#7 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(219): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#8 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#9 E:\TOOLS\laragon\www\control_management\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#10 E:\TOOLS\laragon\www\control_management\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#11 {main} {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1277
Stack trace:
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\TOOLS\\\\larago...', 1277)
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(571): include('E:\\\\TOOLS\\\\larago...')
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('E:\\\\TOOLS\\\\larago...')
#3 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(109): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): collect(Array)
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(219): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(320): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 E:\\TOOLS\\laragon\\www\\control_management\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main} at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
