<fieldset class="fieldset__group mb-4">
    <div class="row">
        <div class="col">
            <?php if(session('status')): ?>
                <div class="alert alert-success" role="alert">
                    <?php echo e(session('status')); ?>

                </div>
            <?php endif; ?>
            <?php if(session('fail')): ?>
                <div class="alert alert-danger" role="alert">
                    <?php echo e(session('fail')); ?>

                </div>
            <?php endif; ?>
            
            <form id="csv_form" action="upload-paylist-csv" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <?php if(Request::is('*new-staff-paylist')): ?>
                    <input type="hidden" name="pay_type" id="pay_type" value="1">
                <?php endif; ?>
                <div class="row align-items-center mb-3">
                    <div class="col-md-3 col-lg-2 text-lg-right">
                        <label for="" class="mb-lg-0">給与CSVファイル</label>
                    </div>
                    <div class="col-md-8">
                        <div class="file-wrapper d-sm-flex align-items-center">
                            
                            
                            <input type="file" name="pay_salary" accept="csv" required class="input-file">
                            <input type="hidden" name="" value="false">
                        </div>
                    </div>
                </div>
                <div class="row align-items-center mb-3">
                    <div class="col-md-3 col-lg-2 text-lg-right">
                        <label for="" class="mb-lg-0">対象支給年月</label>
                    </div>
                    <div class="col-md-8">
                        <div class="form-row">
                            <div class="col-auto" style='margin-right: 14px;'>
                                <select name="salary_type" id="salary_type" class="form-control">
                                    <option value="0" <?php echo e(session('salary_type') == 0 ? 'selected' : ''); ?> selected="selected">月例</option>
                                    <option value="1" <?php echo e(session('salary_type') == 1 ? 'selected' : ''); ?>>賞与</option>
                                </select>
                            </div>
                            <div class="col-auto">
                                <div class="d-flex align-items-center">
                                    <select id="year_dropdown" name="year" class="form-control">
                                    <?php if(session('year')): ?>
                                        <?php for($i = 2009; $i <= date('Y'); $i++): ?>
                                            <option value="<?php echo e($i); ?>" <?php echo e(session('year') == $i ? 'selected' : ''); ?>><?php echo e($i); ?></option>
                                        <?php endfor; ?>
                                    <?php else: ?>
                                        <?php for($year = date('Y') - 13; $year <= date('Y'); $year++): ?>
                                            <option value="<?php echo e($year); ?>" <?php echo e($year == date('Y') ? "selected" :""); ?> ><?php echo e($year); ?></option>
                                        <?php endfor; ?>
                                    <?php endif; ?>
                                    </select>
                                    <span class="mx-2">年</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="d-flex align-items-center">
                                    <select id="month_dropdown" name="month" class="form-control">
                                        <?php if(session('month')): ?>
                                            <?php if(session('salary_type') == 1 ): ?>
                                                <option value="1" <?php echo e(session('month') == 1 ? 'selected' : ''); ?>>①</option>
                                                <option value="2" <?php echo e(session('month') == 2 ? 'selected' : ''); ?>>➁</option>
                                            <?php endif; ?>

                                            <?php if(session('salary_type') == 0 ): ?>
                                                <?php for($i = 1; $i < 13; $i++): ?>
                                                    <option value="<?php echo e($i); ?>" <?php echo e(session('month') == $i ? 'selected' : ''); ?>><?php echo e($i); ?></option>
                                                <?php endfor; ?>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <?php for($month = 1; $month < 13; $month++): ?>
                                                <option value="<?php echo e($month); ?>"><?php echo e($month); ?></option>
                                            <?php endfor; ?>
                                        <?php endif; ?>
                                    </select>
                                    <span class="mx-2 label_month">月</span>                      
                                </div>
                            </div>
                            
                        </div>  
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3 col-lg-2"></div>
                    <div class="col-md-8">
                        <p class="text-danger">
                        取り込み作業完了までしばらく時間が掛かります
                        </p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 col-lg-2"></div>
                    <div class="col-md-8">
                        <button id="csv_submit" class="btn btn-primary" type="submit">取り込み作業開始</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</fieldset>

<div class="row justify-content-center">
    <div class="col-auto text-center">
        <?php if(session('staff')): ?>
            <p><?php echo e(session('staff')); ?></p>
        <?php endif; ?>

        <?php if(session('regular_employee')): ?>
            <p><?php echo e(session('regular_employee')); ?></p>
        <?php endif; ?>
    </div>
</div><?php /**PATH E:\TOOLS\laragon\www\control_management\resources\views/Staff/Paylist/Includes/tab-csv-import.blade.php ENDPATH**/ ?>