<div class="row">
    <?php echo csrf_field(); ?>
    <div class="col-xl-6 col-lg-12 col-md-12 col-12">
        <fieldset class="fieldset__group mb-4">
            <div class="row align-items-center">
                <div class="col-md-3 col-lg-2 text-lg-right">
                    <label for="" class="mb-lg-0">稼働開始日</label>
                </div>
                <div class="col-md-9 col-lg-10">
                    <?php
                        $currentDate = Carbon\Carbon::now();
                        $currentYear = $currentDate->year;
                        $currentMonth = $currentDate->month;
                    ?>
                    <div class="form-row">
                        <div class="col-auto">
                            <div class="d-flex align-items-center">
                                <select id="form_year_dropdown" name="form_year" class="form-control">
                                    <?php for($i = 2008; $i < ($currentYear+2); $i++): ?>
                                        <option value="<?php echo e($i); ?>" <?php echo e($currentYear == $i ? 'selected' : ''); ?>><?php echo e($i); ?></option>
                                    <?php endfor; ?>
                                </select>
                                <span>年</span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex align-items-center">
                                <select id="form_month_dropdown" name="form_month" class="form-control">
                                    <?php for($i = 1; $i <= 12; $i++): ?>
                                        <option value="<?php echo e($i); ?>" <?php echo e(($currentMonth - 1) == $i ? 'selected' : ''); ?>><?php echo e($i); ?></option>
                                    <?php endfor; ?>
                                </select>
                                <span class="mx-2 label_month">月</span>                      
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <span class="mx-2">～</span> 
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex align-items-center">
                                <select id="end_year_dropdown" name="end_year" class="form-control">
                                    <?php for($i = 2008; $i < ($currentYear+2); $i++): ?>
                                        <option value="<?php echo e($i); ?>" <?php echo e($currentYear == $i ? 'selected' : ''); ?>><?php echo e($i); ?></option>
                                    <?php endfor; ?>
                                </select>
                                <span class="mx-2">年</span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex align-items-center">
                                <select id="end_month_dropdown" name="end_month" class="form-control">
                                    <?php for($i = 1; $i <= 12; $i++): ?>
                                        <option value="<?php echo e($i); ?>" <?php echo e(($currentMonth - 1) == $i ? 'selected' : ''); ?>><?php echo e($i); ?></option>
                                    <?php endfor; ?>
                                </select>
                                <span class="mx-2 label_month">月</span>                      
                            </div>
                        </div>
                    </div>  
                </div>
            </div>
            <div class="d-flex justify-content-end mt-4">
                <button id="export_csv_employer_2" class="btn btn-primary mr-2">更新用フォーマットダウンロード</button>
                <button id="export-csv-employer" class="btn btn-primary" >一覧リストダウンロード</button>
            </div>
        </fieldset>
    </div>
    <div class="col-xl-6 col-lg-12 col-md-12 col-12">
        <fieldset class="fieldset__group mb-4">
            <div class="row align-items-center">
                <div class="col-md-3 col-lg-2">
                    <label for="" class="mb-lg-0">社員コードファイル</label>
                </div>
                <div class="col-md-9 col-lg-10">
                    <div class="file-wrapper d-sm-flex align-items-center">
                        
                        
                        <input type="file" name="file_employer_code" id="file_employer_code" required class="input-file d-none" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        <label for="file_employer_code" class="border border-secondary rounded bg-light pl-2 pr-2 mt-2">ファイルを選択</label>
                        <label class="lb_employer h-100 ml-3 mt-2" id="lb-file-select">選択されていません</label>
                    </div>
                </div>
                <div class="col-md-12 mt-2">
                    <button id="import-csv-code" class="btn btn-primary mt-2">取り込み作業開始</button>
                </div>
            </div>
        </fieldset>
    </div>
</div>
<?php /**PATH E:\TOOLS\laragon\www\control_management\resources\views/Staff/Paylist/Includes/tab-employer-code.blade.php ENDPATH**/ ?>