<?php $__env->startPush('plugins-css'); ?>
	<style type="text/css">
		.salary {
			text-align: right;
		}

		.table-header {
			/* vertical-align: middle; */
			text-align: center;
		}
		.th2 {
			background-color: #becdf2;
		}
		.table-header2{
			background-color: #becdf2;
			color: black;		
		}
		@media  screen and (max-width: 767px) {
			.salary-mb {
				padding: 0;
			}
			.salary-mb .table tr th, .salary-mb .table tr td {
				padding: 5px 2px;
				font-size: 8px;
			}
		}

		#payslip-format-create-modal .title-setting{
			height: 30px !important;
		}

		#payslip-format-create-modal .section{
			margin-top: 10px;
			margin-bottom: 15px;
			display: flex;
			width: 100%;
			flex-direction: column;
		}
		#payslip-format-create-modal .section-wrapper{
			width: 100%;
			display: flex;
			flex-wrap: wrap;
		}
		#payslip-format-create-modal .section .section-wrapper .item{
			width: calc(100% / 7 + 1px);
			display:flex;
			flex-direction: column;
			border: 1px solid black;
			margin-top: -1px;
			margin-left: -1px;
		}    

		
		#payslip-format-create-modal .section .section-wrapper .item .title{
			width: 100%;
			height: 45px;
			display: flex;
			background-color: #becdf2;
			justify-content: center;
			align-items: center;
			border-bottom: 1px solid black;
		}
		#payslip-format-create-modal .section .section-wrapper .item .title .title-setting{
			width: 85%;
		}
		#payslip-format-create-modal .section .section-wrapper .item .value{
			width: 100%;
			height: 35px;
			justify-content: center;
			align-items: center;
		}
		#payslip-format-create-modal .addrow-wrapper{
			margin-top: 5px;
			width: 100%;
			display: flex;
			justify-content: flex-end;
		}
		#payslip-format-create-modal .section-4{
			display: flex;
			widows: 100%;
			justify-content: space-between;
            align-items: flex-end;
		}
		#payslip-format-create-modal .section-4 .left{
            width: 100%;
			display: flex;
			flex-direction: column;
		}
		#payslip-format-create-modal .section-4 .right{
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

		}
		#payslip-format-create-modal .section-4 .item{
			display: flex;
			width: 100%;
			border: 1px solid black;
			margin-top: -1px;
			margin-left: -1px;

		}
		#payslip-format-create-modal .section-4 .item .title{
			width: 75%;
			height: 45px;
			display: flex;
			padding-left: 10px;
			padding-right: 10px;
			border-right: 1px solid black;
			justify-content: center;
			align-items: center;
			background-color: #becdf2;
		}
		#payslip-format-create-modal .section-4 .right .table{
			margin: 0px;
		}
		#payslip-format-create-modal .section-4 .right .table td{
			height: 35px;
			padding: 8px;
		}
		
	</style>
<?php $__env->stopPush(); ?>
<div class="tg-modal content-center box--shadow" id="payslip-format-create-modal">
	<div class="container tg-modal__container" id="payslip-format-create-modal-result">
		<div class="col-md-12 salary-mb">
            <form id='frm_payslip_format'>
                <div class="row">
                    <div class="col-md-2">
                        <input type="hidden" id="record_id">
                        <input type="hidden"class="form-control" name="current_format_id" id="current_format_id" placeholder="フォーマットID">
                        <input type="number" class="form-control" name="format_id" id="format_id" title='数字のみ入力可能です' placeholder="フォーマットID">
                    </div>
                    <div class="col-md-5 pl-0">
                        <input type="text"class="form-control" name="format_name" id="format_name" title='好きな名称を入力できます' placeholder="フォーマット名称">
                    </div>
                    <div class="col-md-5 d-flex align-items-center pl-0">
                        <i class="fas fa-question-circle" id="tooltipshow" style="color: #becdf2;font-size:33px;"  data-html="true"  data-toggle="tooltip" data-placement="right" 
                        title="
                            <b class='head'>IDとフォーマット名称はご自身で設定してください。</b><br/>
                            IDは数字のみの入力可能となっております。<br/>
                            フォーマット名称はご自由に入力ください。
                        "
                        >
                        </i>
                    </div>
                </div>
                <div style="margin-top:10px;">
                    <table style="border:black solid 1px;" class="table">
                        <thead>
                            <tr style="background-color:#17a2b8;border:black solid 1px;">
                                <th style="color:black;border:black solid 1px; text-align:center;width:38%" class="th2 width150">部門 ‐ 所属</th>
                                <th style="color:black;border:black solid 1px; text-align:center;width:24%" class="th2 width150">社員コード</th>
                                <th style="color:black;border:black solid 1px;width:38%" class="th2" align="left">氏名</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="border:black solid 1px;">
                                    &nbsp;
                                </td>
                                <td style="border:black solid 1px;">
                                    &nbsp;
                                </td>
                                <td style="border:black solid 1px;">
                                    &nbsp;
                                </td>
                            </tr>
                        </tbody>
                    </table>
            
                    支給
                    <div class="section section-1" data-section_id="1">
                        <div class="section-wrapper">
                            <?php for($i = 1; $i<= 14; $i++): ?>
                                <div class="item">
                                    <div class="title">
                                        <input type="text" name='section_1_' class="form-control title-setting clearable">
                                    </div>
                                    <div class="value">
                                        
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                        <div class="addrow-wrapper">
                            <a class="align-self-center reduceRow" data-count_cell="14" data-num='14' href="javascript:void(0);">
                                <i class="fas fa-2x fa-minus-square color-coal"></i>
                            </a>
                        </div>
                        <div class="addrow-wrapper">
                            <a class="align-self-center addRow" data-count_cell="14" href="javascript:void(0);">
                                <i class="fas fa-2x fa-plus-square color-coal"></i>
                            </a>
                        </div>
                    </div>

                    控除
                    <div class="section section-2" data-section_id="2">
                        <div class="section-wrapper">
                            <?php for($i = 1; $i<= 14; $i++): ?>
                                <div class="item">
                                    <div class="title">
                                        <input type="text" class="form-control title-setting clearable">
                                    </div>
                                    <div class="value">
                                        
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                        <div class="addrow-wrapper">
                            <a class="align-self-center reduceRow" data-count_cell="14" data-num='14' href="javascript:void(0);">
                                <i class="fas fa-2x fa-minus-square color-coal"></i>
                            </a>
                        </div>
                        <div class="addrow-wrapper">
                            <a class="align-self-center addRow" data-count_cell="14" href="javascript:void(0);">
                                <i class="fas fa-2x fa-plus-square color-coal"></i>
                            </a>
                        </div>
                    </div>
            
                    <div class="section section-3" data-section_id="3">
                        <div class="section-wrapper">
                            <?php for($i = 1; $i<= 7; $i++): ?>
                                <div class="item">
                                    <div class="title">
                                        <input type="text" class="form-control title-setting clearable">
                                    </div>
                                    <div class="value">
                                        
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                      
                    </div>
            
                    <div class="section-4">
                            <div class="col-md-2 left">
                                <table id="left_timetable" class="table mb-0">
                                    <tr>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">
                                            <input type="text" name='section4_1' class="form-control title-setting clearable">
                                        </td>
                                    </tr>
                                    <tr><td style="border:black solid 1px; text-align:center;height: 35px;"></td></tr>
                                    <tr>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">
                                            <input type="text" name='section4_2' class="form-control title-setting clearable">
                                        </td>
                                    </tr>
                                    <tr><td style="border:black solid 1px; text-align:center;height: 35px;"></td></tr>
                                    <tr>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">
                                            <input type="text" name='section4_3' class="form-control title-setting clearable">
                                        </td>
                                    </tr>
                                    <tr><td style="border:black solid 1px; text-align:center;height: 35px;"></td></tr>
                                </table>
                            </div>
                            <div class="col-md-10 right">
                                <div class="section-3-button-action">
                                    <div class="addrow-wrapper mt-0">
                                        <a class="align-self-center reduceRow" data-count_cell="7" href="javascript:void(0);">
                                            <i class="fas fa-2x fa-minus-square color-coal"></i>
                                        </a>
                                    </div>
                                    <div class="addrow-wrapper">
                                        <a class="align-self-center addRow" data-count_cell="7" href="javascript:void(0);">
                                            <i class="fas fa-2x fa-plus-square color-coal"></i>
                                        </a>
                                    </div>
                                </div>
                                <table id="time_table" class="table">
                                    <tr>
                                        <td style="border:black solid 1px;" class="th2">&nbsp;</td>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">
                                            <input type="text" name='section4_4' class="form-control title-setting clearable">
                                        </td>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">
                                            <input type="text" name='section4_5' class="form-control title-setting clearable">
                                        </td>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">
                                            <input type="text" name='section4_6' class="form-control title-setting clearable">
                                        </td>
                                        <td style="border:black solid 1px; text-align:center;"class="th2">
                                            <input type="text" name='section4_7' class="form-control title-setting clearable">
                                        </td>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">
                                            <input type="text" name='section4_8' class="form-control title-setting clearable">
                                        </td>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">
                                            <input type="text" name='section4_9' class="form-control title-setting clearable">
                                        </td>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">
                                            <input type="text" name='section4_10' class="form-control title-setting clearable">
                                        </td>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">
                                            <input type="text" name='section4_11' class="form-control title-setting clearable">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">
                                            時間
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:black solid 1px; text-align:center;" class="th2">					
                                            金額
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                        <td style="border:black solid 1px;" align="right">
                                            &nbsp;
                                        </td>
                                    </tr>
                                </table>
                            </div>
                    </div>
                    
                    <table class="table">
                        <tr style="border:none">
                            <td colspan="100" style="border:none; padding-right:0px"  align="right">
                                <button class="btn btn-primary mb-2 mb-sm-0" type="button" id="btn_save_payslip_format">保存</button>
                            </td>
                        </tr>
                    </table>
                </div>
            </form>
		</div>
		
	</div>
</div><?php /**PATH E:\TOOLS\laragon\www\control_management\resources\views/Staff/Paylist/Includes/payslip-format-create-modal.blade.php ENDPATH**/ ?>