<?php
    $disabled = "";
    $checked = "checked";
    if(Request::route()->getName() == 'new-staff-paylist'){
        if(!checkRoleUser(config("constants.ROLE.view-format-employee-pay-slip.alias"))){
            $disabled = "disabled";
            $checked = "";
        }
    }else{
        if(!checkRoleUser(config("constants.ROLE.view-employee-pay-slip.alias"))){
            $disabled = "disabled";
            $checked = "";
        }
    }
    
?>
<fieldset class="fieldset__group mb-4">
    <div class="form-row">
        <div class="form-group col-md-4 col-xl-2">
            <label for="">社員コード</label>
            <input id="cd_jdl_id_textbox" $disabled type="text" class="form-control search" <?php echo e($disabled); ?>>
            <?php if(Request::route()->getName() == 'new-staff-paylist'): ?>
                <input type="hidden" name="pay_type" id="pay_type" value="1">
            <?php endif; ?>
        </div>
        <div class="form-group col-md-4 col-xl-2">
            <label for="">名前</label>
            <input id="cd_name_textbox" $disabled type="text" class="form-control search" <?php echo e($disabled); ?>>
        </div>
        
        <div class="form-group col-md-8 col-lg-4 col-xl-3">
            <label for="">対象支給年月</label>
            <div class="form-row">
                <div class="col-auto flex-grow-1">
                    <div class="d-flex align-items-center">
                        <select name="" id="payment_date_year_dropdown" class="form-control">
                            <option value="0" selected="selected">選択</option>
                            <?php for($year = date('Y') - 12; $year <= date('Y'); $year++): ?>
                                <option value="<?php echo e($year); ?>"><?php echo e($year); ?></option>
                            <?php endfor; ?>
                        </select>
                        <span class="mx-2">年</span>
                    </div>
                </div>
                <div class="col-auto flex-grow-1">
                    <div class="d-flex align-items-center">
                        <select name="" id="payment_date_month_dropdown" class="form-control">
                            <option value="0" selected="selected">選択</option>
                            <?php for($month = 1; $month < 13; $month++): ?>
                                <option value="<?php echo e($month); ?>"><?php echo e($month); ?></option>
                            <?php endfor; ?>
                        </select>
                        <span class="mx-2">月</span>                      
                    </div>
                </div>
            </div>  
        </div>

        

        <div class="form-group col-lg-6 col-md-4 col-xl-4">
            <label for="">営業担当</label>
            <div class="filter-group-container" data-self_id="main_select" data-multiple_mode="true" data-display_type="group">
                <select id="select_box_group_search" class="select2-select">
                </select>
                <input type="hidden" class="group_id" id="group_id_search" value=""/>
            </div>
        </div>

        <div class="form-group col-lg-auto flex-grow-1 align-self-end">
            <input type="submit" id="btn-search-paylist" class="btn btn-block btn-outline" value="検索">
        </div>
        <div class="row mb-0 pb-0 mt-3">
            <div class="col-auto mb-0 mb-md-0 pb-0">
                <div class="form-inline">
                    <div class="mr-5 pb-0">
                        <label>従業員アカウント／スタッフアカウント</label>
                    </div>
                    <div class="custom-control custom-checkbox mr-5 pb-0">
                        <input type="checkbox" class="custom-control-input" name="" id="type-employee-payslip" <?php echo e($checked); ?> <?php echo e($disabled); ?>>
                        <label class="custom-control-label pl-1 pb-0" for="type-employee-payslip">従業員アカウント</label>
                    </div>
                    <div class="custom-control custom-checkbox pb-0 mr-5">
                        <input type="checkbox" class="custom-control-input" name="" id="type-staff-payslip" <?php echo e($checked); ?> <?php echo e($disabled); ?>>
                        <label class="custom-control-label pl-1 pb-0" for="type-staff-payslip">スタッフアカウント</label>
                    </div>
                   
                    <div class="ml-5 mr-5 pb-0">
                        <label>月例/賞与</label>
                    </div>
                    <div class="custom-control custom-checkbox mr-5 pb-0">
                        <input type="checkbox" class="custom-control-input" id="type_salary" <?php echo e($checked); ?> <?php echo e($disabled); ?>>
                        <label class="custom-control-label pl-1 pb-0" for="type_salary">月例</label>
                    </div>
                    <div class="custom-control custom-checkbox pb-0">
                        <input type="checkbox" class="custom-control-input" id="type_bonus" <?php echo e($checked); ?> <?php echo e($disabled); ?>>
                        <label class="custom-control-label pl-1 pb-0" for="type_bonus">賞与</label>
                    </div>
                    
                </div>
            </div>
        </div>
        <div class="col-12">
            <p class="text-danger">
                名前の項目は、複数の単語をスペースで区切ると、複数の単語のどれかが含まれれば検索結果として表示するようにしています。
            </p>
        </div>
    </div>
</fieldset>

<div id="payslip_table">
    <div class="row">
        <div class="col-12">
            <div class="tg-table__container">
                <div class="tg-table__content">
                    <table class="table table-borderless tg-table" id="paylist-table">
                        <thead>
                            <tr>
                                <th class='ctl_check_all'>
                                    <center><div class="custom-control custom-checkbox"><input type="checkbox" name="" class="custom-control-input" value="check_all" id="check_all"><label class="custom-control-label" for="check_all"></label></div></center>
                                </th>
                                <th>社員コード</th>
                                <th>名前</th>
                                <th>フォーマット名</th>
                                
                                <th>対象支給年月</th>
                                <th>部門・所属</th>
                                <th>従業員アカウント／スタッフアカウント</th>
                                <th>
                                    <center>
                                        <button id="payslip_batch" class="btn btn-sm btn-primary">WEB明細一覧</button>
                                        <button id="batch_pdf" class="btn btn-sm btn-primary">PDF明細一覧</button>
                                        <button id="btn_batch_delete_payslip" class="btn btn-sm btn-primary">削除</button>
                                    </center>
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    .custom-control-input {
        width: 15px;
        height: 15px;
        left: 4px;
        top: 3px;
        z-index: 0 !important;
    }
</style><?php /**PATH E:\TOOLS\laragon\www\control_management\resources\views/Staff/Paylist/Includes/tab-payslip.blade.php ENDPATH**/ ?>